# MooMoo.io Enhanced Mod

A comprehensive JavaScript modification for moomoo.io that provides auto-healing, resource management, UI enhancements, and gameplay improvements.

## Features

### 🏥 Auto Healing
- Automatically heals your character when health drops below a configurable threshold
- Configurable health threshold (10-95%)
- Smart cooldown system to prevent spam
- Uses food resources efficiently

### 📊 Resource Tracker
- Real-time display of all resources (Wood, Stone, Food, Gold)
- Health percentage monitoring
- Clean, non-intrusive overlay
- Always visible in the top-right corner

### 🎮 Enhanced UI
- Interactive control panel for mod settings
- Toggle features on/off in real-time
- Adjustable healing threshold slider
- Keyboard shortcuts for quick access

### ⌨️ Keyboard Shortcuts
- **M Key**: Toggle control panel visibility
- **Q Key**: Manual healing (game default, enhanced by mod)

## Installation

### Method 1: Browser Console
1. Open moomoo.io in your browser
2. Press `F12` to open developer tools
3. Go to the Console tab
4. Copy and paste the entire contents of `moomoo-enhanced-mod.js`
5. Press Enter to execute

### Method 2: Userscript (Recommended)
1. Install a userscript manager like <PERSON><PERSON><PERSON><PERSON> or G<PERSON><PERSON>key
2. Create a new userscript
3. Add the following header:
```javascript
// ==UserScript==
// @name         MooMoo.io Enhanced Mod
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  Enhanced mod for moomoo.io with auto-healing and resource tracking
// <AUTHOR> Mod Developer
// @match        *://moomoo.io/*
// @match        *://*.moomoo.io/*
// @grant        none
// ==/UserScript==
```
4. Paste the mod code below the header
5. Save and enable the userscript

### Method 3: Browser Extension
1. Create a browser extension that injects the script
2. Load the mod code into the content script
3. Configure the extension to run on moomoo.io domains

## Usage

### Getting Started
1. Load the mod using one of the installation methods above
2. Join a moomoo.io game
3. The mod will automatically initialize when the game loads
4. Press **M** to open the control panel

### Control Panel
The control panel appears in the top-left corner and includes:
- **Auto Heal Toggle**: Enable/disable automatic healing
- **Resource Tracker Toggle**: Show/hide the resource display
- **Heal Threshold Slider**: Adjust when auto-healing triggers (10-95% health)

### Resource Tracker
The resource tracker appears in the top-right corner showing:
- Current health percentage
- Wood, Stone, Food, and Gold amounts
- Real-time updates

## Configuration

The mod automatically saves your settings to browser localStorage. Settings persist between sessions.

### Default Settings
```javascript
{
    autoHeal: true,           // Auto healing enabled
    autoUpgrade: false,       // Auto upgrades disabled (future feature)
    resourceTracker: true,    // Resource tracking enabled
    enhancedUI: true,         // Enhanced UI enabled
    healThreshold: 80,        // Heal when health drops below 80%
    updateInterval: 100       // Update every 100ms
}
```

### Programmatic Access
You can access the mod programmatically via the browser console:

```javascript
// Access the mod
const mod = window.MooMooEnhancedMod;

// Restart the mod
mod.restart();

// Stop the mod
mod.stop();

// Check current config
console.log(mod.config);

// Access individual components
mod.manager.autoHealer.setThreshold(90);
mod.manager.resourceTracker.enable();
```

## Technical Details

### Architecture
The mod is built with a modular architecture:
- **GameAPI**: Handles interaction with the game
- **AutoHealer**: Manages automatic healing functionality
- **ResourceTracker**: Displays resource information
- **UIEnhancer**: Provides the control panel interface
- **ModManager**: Coordinates all components

### JSDoc Documentation
The entire codebase is documented with comprehensive JSDoc comments including:
- Function parameters and return types
- Class properties and methods
- Usage examples
- Type definitions

### Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Troubleshooting

### Mod Not Loading
1. Ensure you're on the correct moomoo.io domain
2. Check browser console for error messages
3. Try refreshing the page
4. Verify the script was pasted correctly

### Features Not Working
1. Make sure the game has fully loaded
2. Check if the control panel is accessible (press M)
3. Verify settings in the control panel
4. Look for error messages in the browser console

### Performance Issues
1. Increase the update interval in config
2. Disable unused features
3. Check for conflicts with other mods/extensions

## Safety and Fair Play

This mod is designed to enhance the gaming experience while maintaining fair play:
- No speed hacks or movement modifications
- No wall hacks or vision enhancements
- No automated building or combat
- Only quality-of-life improvements

## Contributing

To contribute to this mod:
1. Fork the repository
2. Make your changes with proper JSDoc documentation
3. Test thoroughly
4. Submit a pull request

## License

MIT License - Feel free to modify and distribute

## Changelog

### v1.0.0
- Initial release
- Auto healing functionality
- Resource tracking
- Enhanced UI with control panel
- Keyboard shortcuts
- Configuration persistence
