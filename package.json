{"name": "moomoo-enhanced-mod", "version": "1.0.0", "description": "A comprehensive JavaScript modification for moomoo.io with auto-healing, resource management, and UI enhancements", "main": "moomoo-enhanced-mod.js", "scripts": {"build": "echo 'No build process needed - pure JavaScript'", "lint": "eslint *.js", "docs": "jsdoc -d docs *.js", "test": "echo 'Tests should be run in browser with moomoo.io loaded'"}, "keywords": ["moomoo.io", "game-mod", "javascript", "userscript", "auto-heal", "resource-tracker", "gaming", "browser-game"], "author": "<PERSON><PERSON><PERSON><PERSON> Mod <PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/moomoo-enhanced-mod.git"}, "bugs": {"url": "https://github.com/yourusername/moomoo-enhanced-mod/issues"}, "homepage": "https://github.com/yourusername/moomoo-enhanced-mod#readme", "devDependencies": {"eslint": "^8.0.0", "jsdoc": "^4.0.0"}, "engines": {"node": ">=14.0.0"}, "browserslist": ["Chrome >= 60", "Firefox >= 55", "Safari >= 12", "Edge >= 79"], "files": ["moomoo-enhanced-mod.js", "example-usage.js", "README.md"]}