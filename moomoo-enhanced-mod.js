/**
 * @fileoverview MooMoo.io Enhanced Mod - A comprehensive modification for moomoo.io
 * @description This mod provides auto-healing, resource management, UI enhancements, and gameplay improvements
 * @version 1.0.0
 * <AUTHOR> Mod Developer
 * @license MIT
 */

/**
 * @namespace MooMooMod
 * @description Main namespace for the MooMoo.io Enhanced Mod
 */
const MooMooMod = {
    version: '1.0.0',
    name: 'MooMoo Enhanced Mod',
    initialized: false,
    config: {},
    intervals: {},
    elements: {}
};

/**
 * @typedef {Object} ModConfig
 * @property {boolean} autoHeal - Enable automatic healing
 * @property {boolean} autoUpgrade - Enable automatic upgrades
 * @property {boolean} resourceTracker - Enable resource tracking
 * @property {boolean} enhancedUI - Enable UI enhancements
 * @property {number} healThreshold - Health percentage to trigger healing (0-100)
 * @property {number} updateInterval - Update interval in milliseconds
 */

/**
 * Default configuration for the mod
 * @type {ModConfig}
 */
MooMooMod.defaultConfig = {
    autoHeal: true,
    autoUpgrade: false,
    resourceTracker: true,
    enhancedUI: true,
    healThreshold: 80,
    updateInterval: 100
};

/**
 * @class GameAPI
 * @description Wrapper class for moomoo.io game API interactions
 */
class GameAPI {
    /**
     * Creates an instance of GameAPI
     * @constructor
     */
    constructor() {
        this.player = null;
        this.game = null;
        this.socket = null;
        this.initialized = false;
    }

    /**
     * Initializes the game API by finding game objects
     * @returns {boolean} True if initialization successful
     */
    initialize() {
        try {
            // Find the game object (common patterns in moomoo.io)
            this.game = window.game || window.Game || window.gameManager;
            this.socket = window.io || window.socket;
            
            if (this.game) {
                this.player = this.game.player || this.game.myPlayer;
                this.initialized = true;
                console.log('[MooMoo Mod] Game API initialized successfully');
                return true;
            }
            
            console.warn('[MooMoo Mod] Game objects not found');
            return false;
        } catch (error) {
            console.error('[MooMoo Mod] Failed to initialize Game API:', error);
            return false;
        }
    }

    /**
     * Gets the current player object
     * @returns {Object|null} Player object or null if not available
     */
    getPlayer() {
        return this.player;
    }

    /**
     * Gets player health percentage
     * @returns {number} Health percentage (0-100)
     */
    getHealthPercentage() {
        if (!this.player) return 0;
        return Math.round((this.player.health / this.player.maxHealth) * 100);
    }

    /**
     * Gets player resources
     * @returns {Object} Object containing resource amounts
     */
    getResources() {
        if (!this.player) return {};
        return {
            wood: this.player.wood || 0,
            stone: this.player.stone || 0,
            food: this.player.food || 0,
            gold: this.player.gold || 0
        };
    }

    /**
     * Attempts to heal the player
     * @returns {boolean} True if heal action was triggered
     */
    heal() {
        try {
            if (this.player && this.player.food > 0) {
                // Simulate key press for healing (usually 'Q' key)
                this.simulateKeyPress('KeyQ');
                return true;
            }
            return false;
        } catch (error) {
            console.error('[MooMoo Mod] Failed to heal:', error);
            return false;
        }
    }

    /**
     * Simulates a key press event
     * @param {string} keyCode - The key code to simulate
     */
    simulateKeyPress(keyCode) {
        const event = new KeyboardEvent('keydown', {
            code: keyCode,
            key: keyCode.replace('Key', '').toLowerCase(),
            bubbles: true
        });
        document.dispatchEvent(event);
    }
}

/**
 * @class AutoHealer
 * @description Handles automatic healing functionality
 */
class AutoHealer {
    /**
     * Creates an instance of AutoHealer
     * @param {GameAPI} gameAPI - The game API instance
     * @param {number} threshold - Health threshold percentage
     */
    constructor(gameAPI, threshold = 80) {
        this.gameAPI = gameAPI;
        this.threshold = threshold;
        this.enabled = false;
        this.lastHealTime = 0;
        this.healCooldown = 1000; // 1 second cooldown
    }

    /**
     * Enables auto healing
     */
    enable() {
        this.enabled = true;
        console.log('[MooMoo Mod] Auto healing enabled');
    }

    /**
     * Disables auto healing
     */
    disable() {
        this.enabled = false;
        console.log('[MooMoo Mod] Auto healing disabled');
    }

    /**
     * Updates the healing threshold
     * @param {number} threshold - New threshold percentage (0-100)
     */
    setThreshold(threshold) {
        this.threshold = Math.max(0, Math.min(100, threshold));
    }

    /**
     * Checks if healing is needed and performs healing
     * @returns {boolean} True if healing was performed
     */
    update() {
        if (!this.enabled || !this.gameAPI.initialized) return false;

        const currentTime = Date.now();
        if (currentTime - this.lastHealTime < this.healCooldown) return false;

        const healthPercentage = this.gameAPI.getHealthPercentage();
        
        if (healthPercentage < this.threshold && healthPercentage > 0) {
            if (this.gameAPI.heal()) {
                this.lastHealTime = currentTime;
                console.log(`[MooMoo Mod] Auto healed at ${healthPercentage}% health`);
                return true;
            }
        }
        
        return false;
    }
}

/**
 * @class ResourceTracker
 * @description Tracks and displays player resources
 */
class ResourceTracker {
    /**
     * Creates an instance of ResourceTracker
     * @param {GameAPI} gameAPI - The game API instance
     */
    constructor(gameAPI) {
        this.gameAPI = gameAPI;
        this.enabled = false;
        this.displayElement = null;
    }

    /**
     * Enables resource tracking
     */
    enable() {
        this.enabled = true;
        this.createDisplay();
        console.log('[MooMoo Mod] Resource tracking enabled');
    }

    /**
     * Disables resource tracking
     */
    disable() {
        this.enabled = false;
        if (this.displayElement) {
            this.displayElement.remove();
            this.displayElement = null;
        }
        console.log('[MooMoo Mod] Resource tracking disabled');
    }

    /**
     * Creates the resource display element
     */
    createDisplay() {
        if (this.displayElement) return;

        this.displayElement = document.createElement('div');
        this.displayElement.id = 'moomoo-resource-tracker';
        this.displayElement.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 10000;
            min-width: 150px;
        `;
        
        document.body.appendChild(this.displayElement);
    }

    /**
     * Updates the resource display
     */
    update() {
        if (!this.enabled || !this.displayElement || !this.gameAPI.initialized) return;

        const resources = this.gameAPI.getResources();
        const healthPercentage = this.gameAPI.getHealthPercentage();

        this.displayElement.innerHTML = `
            <div><strong>MooMoo Enhanced Mod</strong></div>
            <div>Health: ${healthPercentage}%</div>
            <div>Wood: ${resources.wood}</div>
            <div>Stone: ${resources.stone}</div>
            <div>Food: ${resources.food}</div>
            <div>Gold: ${resources.gold}</div>
        `;
    }
}

/**
 * @class UIEnhancer
 * @description Provides UI enhancements and mod controls
 */
class UIEnhancer {
    /**
     * Creates an instance of UIEnhancer
     */
    constructor() {
        this.enabled = false;
        this.controlPanel = null;
    }

    /**
     * Enables UI enhancements
     */
    enable() {
        this.enabled = true;
        this.createControlPanel();
        console.log('[MooMoo Mod] UI enhancements enabled');
    }

    /**
     * Disables UI enhancements
     */
    disable() {
        this.enabled = false;
        if (this.controlPanel) {
            this.controlPanel.remove();
            this.controlPanel = null;
        }
        console.log('[MooMoo Mod] UI enhancements disabled');
    }

    /**
     * Creates the mod control panel
     */
    createControlPanel() {
        if (this.controlPanel) return;

        this.controlPanel = document.createElement('div');
        this.controlPanel.id = 'moomoo-control-panel';
        this.controlPanel.style.cssText = `
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            font-size: 12px;
            z-index: 10001;
            min-width: 200px;
            border: 2px solid #4CAF50;
        `;

        this.controlPanel.innerHTML = `
            <div style="margin-bottom: 10px; font-weight: bold; color: #4CAF50;">
                🐄 MooMoo Enhanced Mod v${MooMooMod.version}
            </div>
            <div style="margin-bottom: 8px;">
                <label>
                    <input type="checkbox" id="autoHealToggle" ${MooMooMod.config.autoHeal ? 'checked' : ''}>
                    Auto Heal
                </label>
            </div>
            <div style="margin-bottom: 8px;">
                <label>
                    <input type="checkbox" id="resourceTrackerToggle" ${MooMooMod.config.resourceTracker ? 'checked' : ''}>
                    Resource Tracker
                </label>
            </div>
            <div style="margin-bottom: 8px;">
                <label>
                    Heal Threshold:
                    <input type="range" id="healThresholdSlider" min="10" max="95" value="${MooMooMod.config.healThreshold}" style="width: 80px;">
                    <span id="healThresholdValue">${MooMooMod.config.healThreshold}%</span>
                </label>
            </div>
            <div style="margin-top: 10px; font-size: 10px; color: #888;">
                Press 'M' to toggle this panel
            </div>
        `;

        document.body.appendChild(this.controlPanel);
        this.attachEventListeners();
    }

    /**
     * Attaches event listeners to control panel elements
     */
    attachEventListeners() {
        const autoHealToggle = document.getElementById('autoHealToggle');
        const resourceTrackerToggle = document.getElementById('resourceTrackerToggle');
        const healThresholdSlider = document.getElementById('healThresholdSlider');
        const healThresholdValue = document.getElementById('healThresholdValue');

        if (autoHealToggle) {
            autoHealToggle.addEventListener('change', (e) => {
                MooMooMod.config.autoHeal = e.target.checked;
                if (MooMooMod.autoHealer) {
                    e.target.checked ? MooMooMod.autoHealer.enable() : MooMooMod.autoHealer.disable();
                }
            });
        }

        if (resourceTrackerToggle) {
            resourceTrackerToggle.addEventListener('change', (e) => {
                MooMooMod.config.resourceTracker = e.target.checked;
                if (MooMooMod.resourceTracker) {
                    e.target.checked ? MooMooMod.resourceTracker.enable() : MooMooMod.resourceTracker.disable();
                }
            });
        }

        if (healThresholdSlider && healThresholdValue) {
            healThresholdSlider.addEventListener('input', (e) => {
                const value = parseInt(e.target.value);
                MooMooMod.config.healThreshold = value;
                healThresholdValue.textContent = value + '%';
                if (MooMooMod.autoHealer) {
                    MooMooMod.autoHealer.setThreshold(value);
                }
            });
        }
    }

    /**
     * Toggles the visibility of the control panel
     */
    toggleVisibility() {
        if (this.controlPanel) {
            const isVisible = this.controlPanel.style.display !== 'none';
            this.controlPanel.style.display = isVisible ? 'none' : 'block';
        }
    }
}

/**
 * @class ModManager
 * @description Main manager class for the MooMoo mod
 */
class ModManager {
    /**
     * Creates an instance of ModManager
     */
    constructor() {
        this.gameAPI = new GameAPI();
        this.autoHealer = null;
        this.resourceTracker = null;
        this.uiEnhancer = null;
        this.running = false;
    }

    /**
     * Initializes the mod
     * @returns {Promise<boolean>} True if initialization successful
     */
    async initialize() {
        try {
            console.log('[MooMoo Mod] Initializing Enhanced Mod...');

            // Load configuration
            this.loadConfig();

            // Wait for game to load
            await this.waitForGame();

            // Initialize game API
            if (!this.gameAPI.initialize()) {
                throw new Error('Failed to initialize Game API');
            }

            // Initialize components
            this.autoHealer = new AutoHealer(this.gameAPI, MooMooMod.config.healThreshold);
            this.resourceTracker = new ResourceTracker(this.gameAPI);
            this.uiEnhancer = new UIEnhancer();

            // Store references in main namespace
            MooMooMod.gameAPI = this.gameAPI;
            MooMooMod.autoHealer = this.autoHealer;
            MooMooMod.resourceTracker = this.resourceTracker;
            MooMooMod.uiEnhancer = this.uiEnhancer;

            // Enable features based on config
            if (MooMooMod.config.autoHeal) {
                this.autoHealer.enable();
            }

            if (MooMooMod.config.resourceTracker) {
                this.resourceTracker.enable();
            }

            if (MooMooMod.config.enhancedUI) {
                this.uiEnhancer.enable();
            }

            // Setup keyboard shortcuts
            this.setupKeyboardShortcuts();

            // Start main update loop
            this.startUpdateLoop();

            MooMooMod.initialized = true;
            console.log('[MooMoo Mod] Enhanced Mod initialized successfully!');

            return true;
        } catch (error) {
            console.error('[MooMoo Mod] Failed to initialize:', error);
            return false;
        }
    }

    /**
     * Waits for the game to load
     * @returns {Promise<void>}
     */
    waitForGame() {
        return new Promise((resolve) => {
            const checkGame = () => {
                if (window.game || window.Game || window.gameManager) {
                    resolve();
                } else {
                    setTimeout(checkGame, 500);
                }
            };
            checkGame();
        });
    }

    /**
     * Loads configuration from localStorage or uses defaults
     */
    loadConfig() {
        try {
            const savedConfig = localStorage.getItem('moomoo-enhanced-mod-config');
            if (savedConfig) {
                MooMooMod.config = { ...MooMooMod.defaultConfig, ...JSON.parse(savedConfig) };
            } else {
                MooMooMod.config = { ...MooMooMod.defaultConfig };
            }
        } catch (error) {
            console.warn('[MooMoo Mod] Failed to load config, using defaults:', error);
            MooMooMod.config = { ...MooMooMod.defaultConfig };
        }
    }

    /**
     * Saves configuration to localStorage
     */
    saveConfig() {
        try {
            localStorage.setItem('moomoo-enhanced-mod-config', JSON.stringify(MooMooMod.config));
        } catch (error) {
            console.warn('[MooMoo Mod] Failed to save config:', error);
        }
    }

    /**
     * Sets up keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Toggle control panel with 'M' key
            if (event.code === 'KeyM' && !event.ctrlKey && !event.altKey && !event.shiftKey) {
                if (this.uiEnhancer && this.uiEnhancer.enabled) {
                    this.uiEnhancer.toggleVisibility();
                    event.preventDefault();
                }
            }
        });
    }

    /**
     * Starts the main update loop
     */
    startUpdateLoop() {
        if (this.running) return;

        this.running = true;

        const update = () => {
            if (!this.running) return;

            try {
                // Update auto healer
                if (this.autoHealer) {
                    this.autoHealer.update();
                }

                // Update resource tracker
                if (this.resourceTracker) {
                    this.resourceTracker.update();
                }

                // Save config periodically
                if (Math.random() < 0.001) { // ~0.1% chance per update
                    this.saveConfig();
                }

            } catch (error) {
                console.error('[MooMoo Mod] Error in update loop:', error);
            }

            setTimeout(update, MooMooMod.config.updateInterval);
        };

        update();
        console.log('[MooMoo Mod] Update loop started');
    }

    /**
     * Stops the mod
     */
    stop() {
        this.running = false;

        if (this.autoHealer) {
            this.autoHealer.disable();
        }

        if (this.resourceTracker) {
            this.resourceTracker.disable();
        }

        if (this.uiEnhancer) {
            this.uiEnhancer.disable();
        }

        this.saveConfig();
        console.log('[MooMoo Mod] Enhanced Mod stopped');
    }

    /**
     * Restarts the mod
     */
    async restart() {
        this.stop();
        await new Promise(resolve => setTimeout(resolve, 1000));
        await this.initialize();
    }
}

// Initialize the mod when the script loads
(function() {
    'use strict';

    /**
     * Main initialization function
     * @description Initializes the MooMoo Enhanced Mod when the page is ready
     */
    function initializeMod() {
        // Prevent multiple initializations
        if (window.MooMooEnhancedMod) {
            console.warn('[MooMoo Mod] Mod already initialized');
            return;
        }

        // Create mod manager instance
        const modManager = new ModManager();

        // Store reference globally
        window.MooMooEnhancedMod = {
            manager: modManager,
            version: MooMooMod.version,
            restart: () => modManager.restart(),
            stop: () => modManager.stop(),
            config: MooMooMod.config
        };

        // Initialize the mod
        modManager.initialize().then(success => {
            if (success) {
                console.log(`[MooMoo Mod] 🐄 Enhanced Mod v${MooMooMod.version} loaded successfully!`);
                console.log('[MooMoo Mod] Press "M" to toggle the control panel');
                console.log('[MooMoo Mod] Access mod via window.MooMooEnhancedMod');
            } else {
                console.error('[MooMoo Mod] Failed to load Enhanced Mod');
            }
        });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeMod);
    } else {
        initializeMod();
    }

})();

/**
 * @description Export the main namespace for external access
 * This allows other scripts to interact with the mod
 */
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MooMooMod;
}
