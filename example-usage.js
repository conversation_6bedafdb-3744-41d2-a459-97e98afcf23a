/**
 * @fileoverview Example usage and integration examples for MooMoo.io Enhanced Mod
 * @description This file demonstrates how to use and extend the MooMoo Enhanced Mod
 * @version 1.0.0
 */

/**
 * @example Basic Usage
 * // The mod automatically initializes when loaded
 * // Access it via the global object
 * const mod = window.MooMooEnhancedMod;
 * 
 * // Check if mod is loaded
 * if (mod) {
 *     console.log('Mod version:', mod.version);
 * }
 */

/**
 * @example Programmatic Control
 * // Control auto healing
 * const mod = window.MooMooEnhancedMod;
 * 
 * // Enable auto healing with custom threshold
 * mod.manager.autoHealer.enable();
 * mod.manager.autoHealer.setThreshold(75); // Heal at 75% health
 * 
 * // Disable auto healing
 * mod.manager.autoHealer.disable();
 */

/**
 * @example Configuration Management
 * // Access and modify configuration
 * const mod = window.MooMooEnhancedMod;
 * 
 * // View current config
 * console.log('Current config:', mod.config);
 * 
 * // Modify config
 * mod.config.healThreshold = 85;
 * mod.config.updateInterval = 50; // Faster updates
 * 
 * // Save config
 * mod.manager.saveConfig();
 */

/**
 * @example Custom Event Handlers
 * // Add custom functionality when healing occurs
 * const originalHeal = window.MooMooEnhancedMod.manager.autoHealer.update;
 * 
 * window.MooMooEnhancedMod.manager.autoHealer.update = function() {
 *     const healed = originalHeal.call(this);
 *     if (healed) {
 *         console.log('Auto heal triggered!');
 *         // Add custom logic here
 *     }
 *     return healed;
 * };
 */

/**
 * @example Resource Monitoring
 * // Monitor resources and trigger custom actions
 * function monitorResources() {
 *     const mod = window.MooMooEnhancedMod;
 *     if (!mod || !mod.manager.gameAPI.initialized) return;
 *     
 *     const resources = mod.manager.gameAPI.getResources();
 *     
 *     // Alert when resources are low
 *     if (resources.food < 10) {
 *         console.warn('Low food! Consider gathering more.');
 *     }
 *     
 *     if (resources.wood < 50) {
 *         console.warn('Low wood! Consider chopping trees.');
 *     }
 * }
 * 
 * // Run every 5 seconds
 * setInterval(monitorResources, 5000);
 */

/**
 * @example Custom UI Elements
 * // Add custom UI elements that integrate with the mod
 * function addCustomButton() {
 *     const button = document.createElement('button');
 *     button.textContent = 'Emergency Heal';
 *     button.style.cssText = `
 *         position: fixed;
 *         bottom: 20px;
 *         right: 20px;
 *         z-index: 10000;
 *         padding: 10px;
 *         background: #ff4444;
 *         color: white;
 *         border: none;
 *         border-radius: 5px;
 *         cursor: pointer;
 *     `;
 *     
 *     button.addEventListener('click', () => {
 *         const mod = window.MooMooEnhancedMod;
 *         if (mod && mod.manager.gameAPI.initialized) {
 *             mod.manager.gameAPI.heal();
 *             console.log('Emergency heal activated!');
 *         }
 *     });
 *     
 *     document.body.appendChild(button);
 * }
 * 
 * // Add the button when mod is ready
 * setTimeout(addCustomButton, 2000);
 */

/**
 * @example Mod Extension
 * // Extend the mod with additional functionality
 * class AutoBuilder {
 *     constructor(gameAPI) {
 *         this.gameAPI = gameAPI;
 *         this.enabled = false;
 *     }
 *     
 *     enable() {
 *         this.enabled = true;
 *         console.log('Auto builder enabled');
 *     }
 *     
 *     disable() {
 *         this.enabled = false;
 *         console.log('Auto builder disabled');
 *     }
 *     
 *     update() {
 *         if (!this.enabled || !this.gameAPI.initialized) return;
 *         
 *         // Add auto building logic here
 *         const resources = this.gameAPI.getResources();
 *         
 *         // Example: Auto build walls when wood > 100
 *         if (resources.wood > 100) {
 *             // Trigger building logic
 *             console.log('Auto building triggered');
 *         }
 *     }
 * }
 * 
 * // Integrate with existing mod
 * function addAutoBuilder() {
 *     const mod = window.MooMooEnhancedMod;
 *     if (!mod) return;
 *     
 *     const autoBuilder = new AutoBuilder(mod.manager.gameAPI);
 *     mod.autoBuilder = autoBuilder;
 *     
 *     // Add to update loop
 *     const originalUpdate = mod.manager.startUpdateLoop;
 *     // Integration would require modifying the update loop
 * }
 */

/**
 * @example Debug and Monitoring
 * // Debug utilities for the mod
 * const ModDebug = {
 *     /**
 *      * Logs current mod status
 *      */
 *     status() {
 *         const mod = window.MooMooEnhancedMod;
 *         if (!mod) {
 *             console.log('Mod not loaded');
 *             return;
 *         }
 *         
 *         console.log('=== MooMoo Mod Status ===');
 *         console.log('Version:', mod.version);
 *         console.log('Initialized:', mod.manager.gameAPI.initialized);
 *         console.log('Auto Heal:', mod.manager.autoHealer.enabled);
 *         console.log('Resource Tracker:', mod.manager.resourceTracker.enabled);
 *         console.log('UI Enhancer:', mod.manager.uiEnhancer.enabled);
 *         console.log('Config:', mod.config);
 *         
 *         if (mod.manager.gameAPI.initialized) {
 *             console.log('Health:', mod.manager.gameAPI.getHealthPercentage() + '%');
 *             console.log('Resources:', mod.manager.gameAPI.getResources());
 *         }
 *     },
 *     
 *     /**
 *      * Tests mod functionality
 *      */
 *     test() {
 *         const mod = window.MooMooEnhancedMod;
 *         if (!mod) {
 *             console.error('Mod not loaded');
 *             return;
 *         }
 *         
 *         console.log('Testing mod functionality...');
 *         
 *         // Test healing
 *         console.log('Testing heal function...');
 *         const healResult = mod.manager.gameAPI.heal();
 *         console.log('Heal result:', healResult);
 *         
 *         // Test resource tracking
 *         console.log('Testing resource tracking...');
 *         const resources = mod.manager.gameAPI.getResources();
 *         console.log('Resources:', resources);
 *         
 *         console.log('Test complete');
 *     },
 *     
 *     /**
 *      * Monitors mod performance
 *      */
 *     monitor() {
 *         let updateCount = 0;
 *         let startTime = Date.now();
 *         
 *         const interval = setInterval(() => {
 *             updateCount++;
 *             
 *             if (updateCount % 100 === 0) {
 *                 const elapsed = Date.now() - startTime;
 *                 const updatesPerSecond = (updateCount / elapsed) * 1000;
 *                 console.log(`Mod performance: ${updatesPerSecond.toFixed(2)} updates/sec`);
 *             }
 *         }, 100);
 *         
 *         // Stop monitoring after 30 seconds
 *         setTimeout(() => {
 *             clearInterval(interval);
 *             console.log('Performance monitoring stopped');
 *         }, 30000);
 *     }
 * };
 * 
 * // Make debug utilities available globally
 * window.ModDebug = ModDebug;
 */

/**
 * @example Integration with Other Mods
 * // Example of how to integrate with other moomoo.io mods
 * function integrateWithOtherMods() {
 *     // Check for other common mods
 *     if (window.someOtherMod) {
 *         console.log('Detected other mod, setting up integration...');
 *         
 *         // Example integration logic
 *         const mod = window.MooMooEnhancedMod;
 *         if (mod) {
 *             // Coordinate features to avoid conflicts
 *             // Share resources or data between mods
 *         }
 *     }
 * }
 * 
 * // Run integration check after a delay
 * setTimeout(integrateWithOtherMods, 3000);
 */

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ModDebug,
        // Add other utilities here
    };
}
